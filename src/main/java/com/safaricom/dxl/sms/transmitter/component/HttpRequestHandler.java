package com.safaricom.dxl.sms.transmitter.component;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.function.Consumer;

@Service
@RequiredArgsConstructor
public class HttpRequestHandler {
    private final WebClient webClient;
    private final MsConfigProperties properties;

    public <T, R> Mono<T> post(String url, Consumer<HttpHeaders> headers, R requestBody, Class<T> responseType) {
        return webClient.post()
                .uri(url)
                .headers(headers)
                .body(BodyInserters.fromValue(requestBody))
                .retrieve()
                .bodyToMono(responseType)
                .retryWhen(Retry.backoff(properties.getHttpRetries(), Duration.ofSeconds(1)));
    }
}
