package com.safaricom.dxl.sms.transmitter.component;

import com.safaricom.dxl.sms.transmitter.config.MsConfigProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.net.ConnectException;
import java.time.Duration;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class to verify the improved retry logic in HttpRequestHandler.
 * Tests that only appropriate errors trigger retries (network issues and 5xx errors)
 * while 4xx client errors and other exceptions do not trigger retries.
 */
@ExtendWith(MockitoExtension.class)
class HttpRequestHandlerRetryTest {

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private WebClient.RequestBodySpec requestBodySpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @Mock
    private MsConfigProperties properties;

    private HttpRequestHandler httpRequestHandler;
    private Consumer<HttpHeaders> headersConsumer;
    private String testPayload;

    @BeforeEach
    void setUp() {
        httpRequestHandler = new HttpRequestHandler(webClient, properties);
        headersConsumer = headers -> headers.add("Authorization", "Bearer test-token");
        testPayload = "test-payload";

        // Configure default retry count
        when(properties.getHttpRetries()).thenReturn((short) 3);

        // Set up the WebClient mock chain
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
        when(requestBodySpec.headers(any(Consumer.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.body(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
    }

    @Nested
    class RetryableErrors {

        @Test
        void shouldRetry_whenConnectExceptionOccurs() {
            // Arrange
            ConnectException connectException = new ConnectException("Connection refused");
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(connectException))
                    .thenReturn(Mono.error(connectException))
                    .thenReturn(Mono.just("Success"));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectNext("Success")
                    .verifyComplete();

            // Verify that the request was attempted 3 times (initial + 2 retries)
            verify(responseSpec, times(3)).bodyToMono(String.class);
        }

        @Test
        void shouldRetry_whenTimeoutExceptionOccurs() {
            // Arrange
            TimeoutException timeoutException = new TimeoutException("Request timeout");
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(timeoutException))
                    .thenReturn(Mono.just("Success"));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectNext("Success")
                    .verifyComplete();

            // Verify that the request was attempted 2 times (initial + 1 retry)
            verify(responseSpec, times(2)).bodyToMono(String.class);
        }

        @Test
        void shouldRetry_whenIOExceptionOccurs() {
            // Arrange
            IOException ioException = new IOException("Network error");
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(ioException))
                    .thenReturn(Mono.just("Success"));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectNext("Success")
                    .verifyComplete();

            verify(responseSpec, times(2)).bodyToMono(String.class);
        }

        @Test
        void shouldRetry_when500ServerErrorOccurs() {
            // Arrange
            WebClientResponseException serverError = WebClientResponseException.create(
                    500, "Internal Server Error", HttpHeaders.EMPTY, new byte[0], null);
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(serverError))
                    .thenReturn(Mono.just("Success"));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectNext("Success")
                    .verifyComplete();

            verify(responseSpec, times(2)).bodyToMono(String.class);
        }

        @Test
        void shouldRetry_when502BadGatewayOccurs() {
            // Arrange
            WebClientResponseException badGateway = WebClientResponseException.create(
                    502, "Bad Gateway", HttpHeaders.EMPTY, new byte[0], null);
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(badGateway))
                    .thenReturn(Mono.just("Success"));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectNext("Success")
                    .verifyComplete();

            verify(responseSpec, times(2)).bodyToMono(String.class);
        }

        @Test
        void shouldRetry_when503ServiceUnavailableOccurs() {
            // Arrange
            WebClientResponseException serviceUnavailable = WebClientResponseException.create(
                    503, "Service Unavailable", HttpHeaders.EMPTY, new byte[0], null);
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(serviceUnavailable))
                    .thenReturn(Mono.just("Success"));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectNext("Success")
                    .verifyComplete();

            verify(responseSpec, times(2)).bodyToMono(String.class);
        }
    }

    @Nested
    class NonRetryableErrors {

        @Test
        void shouldNotRetry_when400BadRequestOccurs() {
            // Arrange
            WebClientResponseException badRequest = WebClientResponseException.create(
                    400, "Bad Request", HttpHeaders.EMPTY, new byte[0], null);
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(badRequest));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectError(WebClientResponseException.class)
                    .verify();

            // Verify that the request was attempted only once (no retries)
            verify(responseSpec, times(1)).bodyToMono(String.class);
        }

        @Test
        void shouldNotRetry_when401UnauthorizedOccurs() {
            // Arrange
            WebClientResponseException unauthorized = WebClientResponseException.create(
                    401, "Unauthorized", HttpHeaders.EMPTY, new byte[0], null);
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(unauthorized));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectError(WebClientResponseException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(String.class);
        }

        @Test
        void shouldNotRetry_when403ForbiddenOccurs() {
            // Arrange
            WebClientResponseException forbidden = WebClientResponseException.create(
                    403, "Forbidden", HttpHeaders.EMPTY, new byte[0], null);
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(forbidden));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectError(WebClientResponseException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(String.class);
        }

        @Test
        void shouldNotRetry_when404NotFoundOccurs() {
            // Arrange
            WebClientResponseException notFound = WebClientResponseException.create(
                    404, "Not Found", HttpHeaders.EMPTY, new byte[0], null);
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(notFound));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectError(WebClientResponseException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(String.class);
        }

        @Test
        void shouldNotRetry_whenRuntimeExceptionOccurs() {
            // Arrange
            RuntimeException runtimeException = new RuntimeException("Business logic error");
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(runtimeException));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectError(RuntimeException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(String.class);
        }

        @Test
        void shouldNotRetry_whenIllegalArgumentExceptionOccurs() {
            // Arrange
            IllegalArgumentException illegalArgException = new IllegalArgumentException("Invalid argument");
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(illegalArgException));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectError(IllegalArgumentException.class)
                    .verify();

            verify(responseSpec, times(1)).bodyToMono(String.class);
        }
    }

    @Nested
    class RetryExhaustion {

        @Test
        void shouldFailAfterMaxRetries_whenRetryableErrorPersists() {
            // Arrange
            ConnectException connectException = new ConnectException("Connection refused");
            when(responseSpec.bodyToMono(String.class))
                    .thenReturn(Mono.error(connectException));

            // Act & Assert
            StepVerifier.create(httpRequestHandler.post("http://test.com", headersConsumer, testPayload, String.class))
                    .expectError(ConnectException.class)
                    .verify(Duration.ofSeconds(10)); // Allow time for retries

            // Verify that the request was attempted the maximum number of times (initial + retries)
            verify(responseSpec, times(4)).bodyToMono(String.class); // 1 initial + 3 retries
        }
    }
}
